const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const connectDB = require('./config/database');
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');

const app = express();
const PORT = process.env.PORT || 3000;

// ✅ Security middleware
// Enable Helmet but allow flexibility (disable contentSecurityPolicy if needed for React/Vite)
app.use(helmet({
  crossOriginResourcePolicy: false,
}));

// ✅ Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 mins
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
  message: 'Too many requests from this IP, please try again later.',
});
app.use('/api/', limiter);

// ✅ CORS config
app.use(cors());

// ✅ Body parsers
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ✅ Compression
app.use(compression());

// ✅ Logging with morgan piped into Winston
app.use(morgan('combined', { stream: { write: msg => logger.info(msg.trim()) } }));

// ✅ Health check
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// ✅ API routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/departments', require('./routes/departments'));
app.use('/api/programs', require('./routes/programs'));
app.use('/api/courses', require('./routes/courses'));
app.use('/api/obe-config', require('./routes/obeConfig'));
app.use('/api/assessments', require('./routes/assessments'));
app.use('/api/results', require('./routes/results'));
app.use('/api/attainment', require('./routes/attainment'));
app.use('/api/evidence', require('./routes/evidence'));
app.use('/api/readiness', require('./routes/readiness'));
app.use('/api/reports', require('./routes/reports'));

// ✅ 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
  });
});

// ✅ Global error handler
app.use(errorHandler);

// ✅ Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// ✅ Connect to MongoDB (async, non-blocking)
(async () => {
  try {
    await connectDB();
    logger.info('✅ MongoDB connection established');
  } catch (err) {
    logger.error('❌ Failed to connect to MongoDB:', err.message);
    // Don't crash in dev, but in production you may want to exit
    if (process.env.NODE_ENV === 'production') process.exit(1);
  }
})();

// ✅ Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  logger.info(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
});

// ✅ Handle unhandled promise rejections
process.on('unhandledRejection', (err, _promise) => {
  console.log('Unhandled Promise Rejection:', err.message);
  logger.error('Unhandled Promise Rejection:', err.message);
  // Close server & exit process
  server.close(() => {
    process.exit(1);
  });
});

// ✅ Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log('Uncaught Exception:', err.message);
  logger.error('Uncaught Exception:', err.message);
  console.log('Shutting down...');
  process.exit(1);
});

module.exports = app;
