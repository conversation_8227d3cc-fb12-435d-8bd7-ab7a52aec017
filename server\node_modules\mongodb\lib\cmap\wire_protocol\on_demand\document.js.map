{"version": 3, "file": "document.js", "sourceRoot": "", "sources": ["../../../../src/cmap/wire_protocol/on_demand/document.ts"], "names": [], "mappings": ";;;AAAA,wCAcuB;AAEvB,MAAM,iBAAiB,GAAG;IACxB,IAAI,EAAE,CAAC;IACP,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,CAAC;IACb,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;CACD,CAAC;AA8BX,gBAAgB;AAChB,MAAa,gBAAgB;IAsB3B,YACE,IAAgB,EAChB,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK;IACf,yCAAyC;IACzC,QAAwB;QA1B1B;;;;;;WAMG;QACc,UAAK,GACpB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,wDAAwD;QACvC,eAAU,GAA4B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAkBzE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAA,6BAAsB,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxE,CAAC;IAED,wCAAwC;IAChC,aAAa,CAAC,IAAY,EAAE,OAAoB;QACtD,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU;YAAE,OAAO,KAAK,CAAC;QAE7C,MAAM,OAAO,GAAG,UAAU,GAAG,UAAU,CAAC;QACxC,KACE,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,CAAC,EACzC,SAAS,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,GAAG,OAAO,EAC9C,SAAS,EAAE,EAAE,SAAS,EAAE,EACxB,CAAC;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gBAAE,OAAO,KAAK,CAAC;QACxE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG;IACK,UAAU,CAAC,IAAqB;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,aAAa,KAAK,KAAK;YAAE,OAAO,IAAI,CAAC;QAEzC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBAChC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACpC,MAAM,aAAa,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;oBACpD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;oBACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;oBAC7B,OAAO,aAAa,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAErC,kEAAkE;YAClE,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACrE,MAAM,aAAa,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBACpD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;gBACjC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;gBAC9B,OAAO,aAAa,CAAC;YACvB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAcO,SAAS,CAAC,OAAoB,EAAE,EAAkB;QACxD,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,QAAQ,EAAE,EAAE,CAAC;YACX,KAAK,eAAQ,CAAC,IAAI,CAAC;YACnB,KAAK,eAAQ,CAAC,SAAS;gBACrB,OAAO,IAAI,CAAC;YACd,KAAK,eAAQ,CAAC,MAAM;gBAClB,OAAO,IAAA,mBAAY,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACzC,KAAK,eAAQ,CAAC,GAAG;gBACf,OAAO,IAAA,iBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACvC,KAAK,eAAQ,CAAC,IAAI;gBAChB,OAAO,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC1C,KAAK,eAAQ,CAAC,IAAI;gBAChB,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACpC,KAAK,eAAQ,CAAC,QAAQ;gBACpB,OAAO,IAAI,eAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;YAC/D,KAAK,eAAQ,CAAC,SAAS;gBACrB,OAAO,IAAI,gBAAS,CAAC,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YACzD,KAAK,eAAQ,CAAC,MAAM;gBAClB,OAAO,IAAA,aAAM,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;YACnE,KAAK,eAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtB,MAAM,eAAe,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACtD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAEtC,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;oBAClB,MAAM,kBAAkB,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjE,IAAI,kBAAkB,GAAG,CAAC;wBACxB,MAAM,IAAI,gBAAS,CAAC,0DAA0D,CAAC,CAAC;oBAClF,IAAI,kBAAkB,GAAG,eAAe,GAAG,CAAC;wBAC1C,MAAM,IAAI,gBAAS,CAAC,6DAA6D,CAAC,CAAC;oBACrF,IAAI,kBAAkB,GAAG,eAAe,GAAG,CAAC;wBAC1C,MAAM,IAAI,gBAAS,CAAC,8DAA8D,CAAC,CAAC;oBACtF,OAAO,IAAI,aAAM,CACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,EAC/E,CAAC,CACF,CAAC;gBACJ,CAAC;gBAED,OAAO,IAAI,aAAM,CACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,EACpE,OAAO,CACR,CAAC;YACJ,CAAC;YACD,KAAK,eAAQ,CAAC,IAAI;gBAChB,2BAA2B;gBAC3B,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAE5D,KAAK,eAAQ,CAAC,MAAM;gBAClB,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACjD,KAAK,eAAQ,CAAC,KAAK;gBACjB,OAAO,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAEvD;gBACE,MAAM,IAAI,gBAAS,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,IAAI;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED;;;;;;;;OAQG;IACI,GAAG,CAAC,IAAY;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,aAAa,KAAK,KAAK;YAAE,OAAO,KAAK,CAAC;QAC1C,IAAI,aAAa,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC;QACvC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACvC,CAAC;IAuBM,GAAG,CACR,IAAqB,EACrB,EAAK,EACL,QAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACtB,MAAM,IAAI,gBAAS,CAAC,iBAAiB,IAAI,cAAc,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAClD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,IAAI,gBAAS,CAAC,iBAAiB,IAAI,cAAc,CAAC,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,sCAAsC;YACtC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACxB,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,CAAC;IACvB,CAAC;IAiBM,SAAS,CAAC,IAAY,EAAE,QAAiB;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,IAAI,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,IAAI,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE1D,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;QAE/F,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACxC,MAAM,IAAI,gBAAS,CAAC,iBAAiB,IAAI,cAAc,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,OAA4C;QAC1D,OAAO,IAAA,kBAAW,EAAC,IAAI,CAAC,IAAI,EAAE;YAC5B,GAAG,OAAO;YACV,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,gCAAgC,EAAE,IAAI;SACvC,CAAC,CAAC;IACL,CAAC;IAED,yCAAyC;IACzC,OAAO;QACL,MAAM,IAAI,GAAG,IAAA,iBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;IAC7D,CAAC;CACF;AAhTD,4CAgTC"}