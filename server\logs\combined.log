{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:40:45:4045"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:40:45:4045"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:40:58:4058"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:41:25:4125"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:41:25:4125"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:47:01:471"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 21:47:09:479"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mServer will continue without database connection\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mTo fix this:\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m1. Install MongoDB locally, or\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m2. Use MongoDB Atlas (cloud database), or\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m3. Update MONGODB_URI in .env file\u001b[39m","timestamp":"2025-08-18 22:27:01:271"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:27:36:2736"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:27:37:2737"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:29:06:296"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:29:55:2955"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:32:52:3252"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:33:20:3320"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:33:50:3350"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:34:40:3440"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:35:32:3532"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:36:31:3631"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:36:41:3641"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:36:52:3652"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:37:02:372"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:37:22:3722"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:38:03:383"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:38:14:3814"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:38:42:3842"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:40:20:4020"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:40:22:4022"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:40:35:4035"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:40:37:4037"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:40:40:4040"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:41:05:415"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:41:11:4111"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:41:15:4115"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:41:17:4117"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:41:47:4147"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB:\u001b[39m","timestamp":"2025-08-18 22:42:11:4211"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m❌ Error connecting to MongoDB: option buffermaxentries is not supported\u001b[39m","timestamp":"2025-08-18 22:48:20:4820"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000 in development mode\u001b[39m","timestamp":"2025-08-18 23:03:40:340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000 in development mode\u001b[39m","timestamp":"2025-08-18 23:03:55:355"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000 in development mode\u001b[39m","timestamp":"2025-08-18 23:10:00:100"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB Connected: ac-8bqqqtu-shard-00-01.b6rwdhf.mongodb.net\u001b[39m","timestamp":"2025-08-18 23:10:01:101"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB connection established\u001b[39m","timestamp":"2025-08-18 23:10:01:101"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000 in development mode\u001b[39m","timestamp":"2025-08-18 23:10:32:1032"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB Connected: ac-8bqqqtu-shard-00-00.b6rwdhf.mongodb.net\u001b[39m","timestamp":"2025-08-18 23:10:35:1035"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB connection established\u001b[39m","timestamp":"2025-08-18 23:10:35:1035"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::ffff:127.0.0.1 - - [18/Aug/2025:17:40:56 +0000] \"GET /health HTTP/1.1\" 200 102 \"-\" \"curl/8.1.2\"\u001b[39m","timestamp":"2025-08-18 23:10:56:1056"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [18/Aug/2025:17:41:02 +0000] \"GET /health HTTP/1.1\" 200 102 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-18 23:11:02:112"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m::1 - - [18/Aug/2025:17:43:04 +0000] \"POST /api/auth/login HTTP/1.1\" 401 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\u001b[39m","timestamp":"2025-08-18 23:13:04:134"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 Server running on port 5000 in development mode\u001b[39m","timestamp":"2025-08-18 23:14:49:1449"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB Connected: ac-8bqqqtu-shard-00-00.b6rwdhf.mongodb.net\u001b[39m","timestamp":"2025-08-18 23:14:50:1450"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m✅ MongoDB connection established\u001b[39m","timestamp":"2025-08-18 23:14:50:1450"}
