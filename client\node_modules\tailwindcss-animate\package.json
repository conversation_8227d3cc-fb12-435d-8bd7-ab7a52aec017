{"name": "tailwindcss-animate", "version": "1.0.7", "description": "A Tailwind CSS plugin for creating beautiful animations.", "main": "index.js", "files": ["index.js", "index.d.ts"], "scripts": {"format": "prettier --write '**'", "format:check": "prettier --check '**'", "prepare": "husky install"}, "keywords": ["tailwind", "tailwindcss", "css", "postcss", "plugin", "animation", "transition", "animate", "animated", "animatecss", "animate.css", "fade", "slide", "zoom", "spin", "opacity", "transform", "translate", "scale"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"tailwindcss": ">=3.0.0 || insiders"}, "devDependencies": {"husky": "^7.0.4", "lint-staged": "^12.3.4", "prettier": "^2.5.1", "tailwindcss": "^3.0.22"}, "lint-staged": {"*.**": "prettier --write"}}